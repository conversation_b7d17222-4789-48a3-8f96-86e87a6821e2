package main

import (
	"net/http"
	"os"
	"test/collector"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
)

func main() {

	collector := &collector.MyCollector{}
	collector.Start()

	prometheus.MustRegister(collector)

	http.Handle("/metrics", promhttp.Handler())
	logrus.Infof("nodeName : %s exporter server start", os.Getenv("NODE_NAME"))
	http.ListenAndServe(":9200", nil)
}
