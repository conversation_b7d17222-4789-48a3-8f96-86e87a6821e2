apiVersion: v1
kind: ServiceAccount
metadata:
  name: hero-exporter-sa
  namespace: monitoring
---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: hero-exporter-cr
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["list"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get"]

---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: hero-exporter-crb
subjects:
- kind: ServiceAccount
  name: hero-exporter-sa
  namespace: monitoring
roleRef:
  kind: ClusterRole
  name: hero-exporter-cr
  apiGroup: rbac.authorization.k8s.io

---

apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: hero-exporter
  namespace: monitoring
  labels:
    app: hero-exporter
spec:
  selector:
    matchLabels:
      app: hero-exporter
  template:
    metadata:
      labels:
        app: hero-exporter
    spec:
      serviceAccountName: hero-exporter-sa
      nodeSelector:
        node-role.kubernetes.io/gpu: "true"
      volumes:
      - hostPath:
          path: /sys
        name: sys
      containers:
      - name: hero-exporter
        image: registry.cnbita.com:5000/wuyiqiang/hero-exporter:v18
        ports:
        - containerPort: 9200
        volumeMounts:
          - mountPath: /sys
            mountPropagation: HostToContainer
            name: sys
            readOnly: true
        env:
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName