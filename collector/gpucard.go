package collector

import (
	"context"
	"os"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

func init() {
	registerCollector("gpuhour", defaultEnabled, NewGPUTotalHoursCollector)
}

var (
	GpucardUpdateDuration  = 5 * time.Minute
	GpucardCleanDuration   = GpucardUpdateDuration * 4
	GpucardExpiredDuration = GpucardUpdateDuration * 3
	UserNameSpace          = "hero-user"
	MatchedResourceName    = "nvidia.com"
	NodeName               = os.Getenv("NODE_NAME")
)

type ResourceStats struct {
	ResourceCount int64
	PodGpuHour    float64
}

type NodeResourceStats struct {
	Pods         map[string]*ResourceStats
	NodeGpuHour  float64
	ResourceName string
}

type GPUTotalHoursCollector struct {
	clientset   *kubernetes.Clientset
	desc        *prometheus.Desc
	subsystem   string
	podGpuStats NodeResourceStats
}

func NewGPUTotalHoursCollector() (Collector, error) {
	config, err := rest.InClusterConfig()
	// config, err := clientcmd.BuildConfigFromFlags("", "/home/<USER>/.kube/config.suanfa")
	if err != nil {
		logrus.Errorf("Error building kubeconfig: %v", err)
		return nil, err
	}
	K8sClientSet := kubernetes.NewForConfigOrDie(config)

	return &GPUTotalHoursCollector{
		clientset: K8sClientSet,
		desc: prometheus.NewDesc(
			"leinao_node_gpu_time_total",
			"node gpu hour total",
			[]string{"node", "resource"}, nil,
		),
		subsystem:   "gpuhour",
		podGpuStats: NodeResourceStats{Pods: make(map[string]*ResourceStats)},
	}, nil
}

func (g *GPUTotalHoursCollector) getNodeResourceName() string {
	node, err := g.clientset.CoreV1().Nodes().Get(context.TODO(), NodeName, metav1.GetOptions{})
	if err != nil {
		logrus.Errorf("Error getting node %s: %v", NodeName, err)
	}
	for resourceName, quantity := range node.Status.Allocatable {
		if strings.Contains(resourceName.String(), MatchedResourceName) && quantity.Value() > 0 {
			return resourceName.String()
		}
	}
	return "nocard"
}

func (g *GPUTotalHoursCollector) Update(tc time.Time) {
	listOptions := metav1.ListOptions{
		FieldSelector: "spec.nodeName=" + NodeName,
	}

	pods, err := g.clientset.CoreV1().Pods(UserNameSpace).List(context.TODO(), listOptions)
	if err != nil {
		logrus.Errorf("Error listing pods: %v", err)
	}
	g.getGPUCountByNode(pods.Items, GpucardUpdateDuration.Hours())
	g.syncNodeTimeHour()

	if g.podGpuStats.ResourceName == "" {
		g.podGpuStats.ResourceName = g.getNodeResourceName()
	}
	MetricsBuffer[GPUHOUR].mtx.Lock()
	defer MetricsBuffer[GPUHOUR].mtx.Unlock()

	tempMetric := &Metric{ExpiredTime: tc.Add(GpucardExpiredDuration)}
	tempMetric.Buffer = append(tempMetric.Buffer,
		prometheus.NewMetricWithTimestamp(tc, prometheus.MustNewConstMetric(g.desc, prometheus.CounterValue, g.podGpuStats.NodeGpuHour, NodeName, g.podGpuStats.ResourceName)))
	MetricsBuffer[GPUHOUR].Metrics[tc.Unix()] = tempMetric
}

func (g *GPUTotalHoursCollector) Run() error {
	ticker := time.NewTicker(GpucardUpdateDuration)
	logrus.Infof("collecter: %s run start", GPUHOUR)
	for tc := range ticker.C {
		go func(tc time.Time) {
			g.Update(tc)
		}(tc)
	}
	return nil
}

func (g *GPUTotalHoursCollector) Clean() {
	ticker := time.NewTicker(GpucardCleanDuration)
	logrus.Infof("collecter: %s clean start", GPUHOUR)
	for tc := range ticker.C {
		MetricsBuffer[GPUHOUR].mtx.Lock()
		for bb, metric := range MetricsBuffer[GPUHOUR].Metrics {
			expiredtime := metric.ExpiredTime
			if expiredtime.Before(tc) {
				logrus.Infof("collecter: %s clean buffer timestamp %d", GPUHOUR, bb)
				delete(MetricsBuffer[GPUHOUR].Metrics, bb)
			}
		}
		MetricsBuffer[GPUHOUR].mtx.Unlock()
	}
}

func (g *GPUTotalHoursCollector) syncNodeTimeHour() {
	for _, podStats := range g.podGpuStats.Pods {
		g.podGpuStats.NodeGpuHour += podStats.PodGpuHour
	}
}

func (g *GPUTotalHoursCollector) getGPUCountByNode(pods []corev1.Pod, duration float64) {
	for _, pod := range pods {
		switch pod.Status.Phase {
		case corev1.PodRunning:
			for _, container := range pod.Spec.Containers {
				for resourceName, quantity := range container.Resources.Requests {
					if strings.Contains(resourceName.String(), MatchedResourceName) {
						if _, exists := g.podGpuStats.Pods[pod.Name]; exists {
							g.podGpuStats.Pods[pod.Name].PodGpuHour = duration * float64(quantity.Value())
						} else {
							g.podGpuStats.ResourceName = string(resourceName)
							g.podGpuStats.Pods[pod.Name] = &ResourceStats{
								ResourceCount: quantity.Value(),
							}
						}

					}
				}
			}
		case corev1.PodFailed:
			if _, exists := g.podGpuStats.Pods[pod.Name]; exists {
				delete(g.podGpuStats.Pods, pod.Name)
			}
		case corev1.PodPending:

		}
	}

}
