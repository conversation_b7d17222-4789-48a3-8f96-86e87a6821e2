package collector

import (
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
)

const (
	defaultEnabled  = true
	defaultDisabled = false

	GPUHOUR    = "gpuhour"
	INFINIBAND = "infiniband"
)

var (
	NameSpace     = "leinao"
	MetricsBuffer = make(map[string]*MetricsData, 0)

	factories          = make(map[string]func() (Collector, error))
	scrapeDurationDesc = prometheus.NewDesc(
		prometheus.BuildFQName(NameSpace, "scrape", "collector_duration_seconds"),
		"node_exporter: Duration of a collector scrape.",
		[]string{"collector"},
		nil,
	)
	scrapeSuccessDesc = prometheus.NewDesc(
		prometheus.BuildFQName(NameSpace, "scrape", "collector_success"),
		"node_exporter: Whether a collector succeeded.",
		[]string{"collector"},
		nil,
	)
)

type Collector interface {
	Update(tc time.Time)
	Run() error
	Clean()
}

type MyCollector struct {
}

func (c *MyCollector) Describe(ch chan<- *prometheus.Desc) {
	ch <- scrapeDurationDesc
	ch <- scrapeSuccessDesc
}

func (c *MyCollector) Collect(ch chan<- prometheus.Metric) {
	ch <- prometheus.NewMetricWithTimestamp(time.Now(), prometheus.MustNewConstMetric(scrapeSuccessDesc, prometheus.CounterValue, float64(1), "collecter"))
	for _, metricsData := range MetricsBuffer {
		metricsData.mtx.Lock()
		for tc, metric := range metricsData.Metrics {
			for _, value := range metric.Buffer {
				ch <- value
			}
			delete(metricsData.Metrics, tc)
		}
		metricsData.mtx.Unlock()
	}
}

func (c *MyCollector) Start() {
	for collectorName, newCollector := range factories {
		go func() {
			logrus.Infof("collecter: %s starts collecting", collectorName)
			collector, _ := newCollector()
			collector.Run()
			collector.Clean()
		}()
	}
}

type Metric struct {
	Buffer      []prometheus.Metric
	ExpiredTime time.Time
}

type MetricsData struct {
	Metrics map[int64]*Metric
	mtx     sync.Mutex
}

func registerCollector(collector string, isDefaultEnabled bool, factory func() (Collector, error)) {
	var helpDefaultState string
	if isDefaultEnabled {
		helpDefaultState = "enabled"
		factories[collector] = factory
		MetricsBuffer[collector] = &MetricsData{Metrics: map[int64]*Metric{}}
	} else {
		helpDefaultState = "disabled"
	}

	logrus.Infof("collector.%s %s", collector, helpDefaultState)
}
